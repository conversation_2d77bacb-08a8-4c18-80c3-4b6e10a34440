import json
import re
import boto3
import os

from botocore.exceptions import ClientError
from ..common.aria_helper.boto3_utils import get_secret

common_prefix = os.environ['COMMON_PREFIX']

def lambda_handler(event, context):
    print(event)
    # Extract token from petition
    token = event['headers']['authorization']
    token = re.sub("Bearer ", "", token)
    # Extract list of valid tokens
    valid_tokens = get_secret(f'{common_prefix}-api_gw_valid_tokens')
    valid_tokens = json.loads(valid_tokens)

    # Check if token is valid
    response = {
        "isAuthorized": True if token in valid_tokens['valid_tokens'] else False
    }

    return response