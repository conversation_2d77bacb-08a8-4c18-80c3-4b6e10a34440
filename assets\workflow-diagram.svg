<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .box { fill: #f8f9fa; stroke: #343a40; stroke-width: 2; }
      .decision { fill: #fff3cd; stroke: #856404; stroke-width: 2; }
      .process { fill: #d1ecf1; stroke: #0c5460; stroke-width: 2; }
      .endpoint { fill: #d4edda; stroke: #155724; stroke-width: 2; }
      .error { fill: #f8d7da; stroke: #721c24; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .arrow { stroke: #343a40; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#343a40" />
    </marker>
  </defs>
  
  <!-- HTTP Request -->
  <rect x="450" y="20" width="100" height="40" class="box" />
  <text x="500" y="45" class="text">HTTP Request</text>
  
  <!-- lambda_authorizer -->
  <rect x="430" y="100" width="140" height="40" class="process" />
  <text x="500" y="125" class="text">lambda_authorizer</text>
  
  <!-- bre_handler -->
  <rect x="450" y="180" width="100" height="40" class="process" />
  <text x="500" y="205" class="text">bre_handler</text>
  
  <!-- Action Present Decision -->
  <polygon points="500,260 550,290 500,320 450,290" class="decision" />
  <text x="500" y="295" class="text">Action?</text>
  
  <!-- No Action Path -->
  <polygon points="300,380 350,410 300,440 250,410" class="decision" />
  <text x="300" y="415" class="text">OCR?</text>
  
  <!-- llm_extractor -->
  <rect x="230" y="500" width="140" height="40" class="process" />
  <text x="300" y="525" class="text">llm_extractor</text>
  
  <!-- Action Path -->
  <rect x="650" y="380" width="120" height="40" class="process" />
  <text x="710" y="405" class="text">Action Config</text>
  
  <!-- BRE Type Decision -->
  <polygon points="710,460 760,490 710,520 660,490" class="decision" />
  <text x="710" y="495" class="text">BRE Type?</text>
  
  <!-- bre -->
  <rect x="800" y="500" width="80" height="40" class="process" />
  <text x="840" y="525" class="text">bre</text>
  
  <!-- bre_validation -->
  <rect x="430" y="580" width="140" height="40" class="process" />
  <text x="500" y="605" class="text">bre_validation</text>
  
  <!-- Final Decision -->
  <polygon points="500,650 550,680 500,710 450,680" class="decision" />
  <text x="500" y="685" class="text">Valid?</text>
  
  <!-- Success Endpoints -->
  <rect x="350" y="750" width="120" height="40" class="endpoint" />
  <text x="410" y="775" class="text">ARIA/DMS</text>
  
  <!-- Review Endpoint -->
  <rect x="530" y="750" width="120" height="40" class="error" />
  <text x="590" y="775" class="text">Manual Review</text>
  
  <!-- Exception Path -->
  <rect x="80" y="500" width="120" height="40" class="error" />
  <text x="140" y="525" class="text">Exception Status</text>
  
  <!-- ARIA Response -->
  <rect x="780" y="580" width="120" height="40" class="endpoint" />
  <text x="840" y="605" class="text">ARIA Response</text>
  
  <!-- Arrows -->
  <line x1="500" y1="60" x2="500" y2="100" class="arrow" />
  <line x1="500" y1="140" x2="500" y2="180" class="arrow" />
  <line x1="500" y1="220" x2="500" y2="260" class="arrow" />
  
  <!-- No Action Path -->
  <line x1="470" y1="290" x2="330" y2="380" class="arrow" />
  <text x="380" y="340" class="text" style="font-size: 10px;">No Action</text>
  
  <!-- Action Path -->
  <line x1="530" y1="290" x2="680" y2="380" class="arrow" />
  <text x="620" y="340" class="text" style="font-size: 10px;">Action Present</text>
  
  <!-- OCR Available -->
  <line x1="320" y1="430" x2="300" y2="500" class="arrow" />
  <text x="320" y="470" class="text" style="font-size: 10px;">Yes</text>
  
  <!-- No OCR -->
  <line x1="270" y1="410" x2="160" y2="500" class="arrow" />
  <text x="200" y="460" class="text" style="font-size: 10px;">No</text>
  
  <!-- llm_extractor to bre_validation -->
  <line x1="350" y1="540" x2="470" y2="580" class="arrow" />
  
  <!-- Action Config to BRE Type -->
  <line x1="710" y1="420" x2="710" y2="460" class="arrow" />
  
  <!-- BRE Type to bre -->
  <line x1="740" y1="490" x2="820" y2="500" class="arrow" />
  <text x="790" y="485" class="text" style="font-size: 10px;">post</text>
  
  <!-- BRE Type to bre_validation -->
  <line x1="680" y1="490" x2="550" y2="580" class="arrow" />
  <text x="600" y="540" class="text" style="font-size: 10px;">validation</text>
  
  <!-- bre to ARIA Response -->
  <line x1="840" y1="540" x2="840" y2="580" class="arrow" />
  
  <!-- bre_validation to Final Decision -->
  <line x1="500" y1="620" x2="500" y2="650" class="arrow" />
  
  <!-- Final Decision to Success -->
  <line x1="480" y1="680" x2="430" y2="750" class="arrow" />
  <text x="440" y="720" class="text" style="font-size: 10px;">Valid</text>
  
  <!-- Final Decision to Review -->
  <line x1="520" y1="680" x2="570" y2="750" class="arrow" />
  <text x="560" y="720" class="text" style="font-size: 10px;">Invalid</text>
  
</svg>
