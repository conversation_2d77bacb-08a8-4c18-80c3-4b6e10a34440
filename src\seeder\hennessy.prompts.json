[{"_id": {"$oid": "687e81e009904e8924b37139"}, "document_type": "bill_of_sale", "content": "You are an intelligent data extraction assistant. Your task is to extract and normalize the following fields from the vehicle sales document text below, even if the field labels are missing, partial, abbreviated, or formatted differently.\n\nPlease extract the following fields and return them in the exact format as a valid JSON object:\n\n- Deal Number\n- Stock Number\n- VIN\n- Year\n- Make\n- Model\n- Odometer Reading\n- Buyer Name\n- Co-Buyer Name\n- Buyer Address\n- Sale Price\n- TAVT / Tax Amount\n- Trade-In Value\n- Total Amount Due (optional)\n- Lien Holder Name\n- Dealer Fees\n\nExtract Year, Make, and Model as separate fields.\n\n---\n\nExamples of label variations to recognize:\n\n- Deal Number: Deal No., DLN, DN, Deal #\n- Stock Number: STK#, STK No., Stock No.\n- VIN: VIN No., VIN #, Vehicle ID, Vehicle Identification Number\n- Year / Make / Model: YM/M, YMM, YR/MAKE/MODEL, Vehicle Info\n- Odometer Reading: ODO, Miles, Mileage, Odometer\n- Buyer Name: Buyer, Purchaser, Customer, Client\n- Co-Buyer Name: Co-Buyer, Cosigner, Co Purchaser\n- Buyer Address: Address, Home Address, Residence, City, ZIP, State\n- Sale Price: Cash Price, Purchase Price, Base Price, Selling Price, Total Price\n- TAVT / Tax Amount: TAVT, Sales Tax, Tax Amount, Tax Due, Tax Total\n- Trade-In Value: Trade, Trade-In, Trade Value\n- Total Amount Due: Balance Due, Total Due\n- Lien Holder Name: Lien, Lien Holder\n- Dealer Fees: Doc Fee, Documentation Fee, Processing Fee, Dealer Fee\n\nYou may infer a field even if it is not explicitly labeled, based on context or common layout patterns.\n\n⚠️ Normalize all values to the field names above.  \n⚠️ If a value is missing or unknown, return it as \"N/A\".  \n✅ Output ONLY valid JSON — no explanation or comments.\n\n--- DOCUMENT TEXT START ---\n{{ document_text }}\n--- DOCUMENT TEXT END ---\n", "template_name": "bill_of_sale.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.721Z"}}, {"_id": {"$oid": "687e81e009904e8924b3713b"}, "document_type": "driver_license", "content": "You are a data extraction assistant. Extract and normalize the following fields from the text of a U.S. driver's license:\n\n- Full Name\n- Date of Birth\n- Address\n- City\n- State\n- ZIP\n- Driver's License Number\n- Expiration Date\n\nRecognize alternate labels:\n- Full Name: Name, Driver Name\n- Date of Birth: DOB, Birth Date\n- Address: Street Address, Mailing Address, Residence, Address (Street)\n- Driver's License Number: DLN, License No, License #\n- Expiration Date: Expiry, Expiration, Expires, EXP, Exp Date, EXPIRATION DATE\n\n🛑 The expiration date might be found as \"EXP 08/04/2030\" — be sure to extract this format even if not labeled.\n\nUse \"N/A\" if any value is missing or not found.\n\n⚠️ Respond with valid, clean JSON only. No explanations or formatting.\n\n--- TEXT START ---\n{{ document_text }}\n--- TEXT END ---\n", "template_name": "driver_license.j2", "uploaded_at": {"$date": "2025-07-22T04:58:21.934Z"}}, {"_id": {"$oid": "687e81e009904e8924b3713d"}, "document_type": "title_application", "content": "You are an intelligent form extractor. Extract and normalize the following fields from the MV-1 document text below.\n\nReturn a clean JSON with these exact keys:\n\n- Buyer Full Name\n- Co-Buyer Name\n- Buyer Address\n- City\n- State\n- ZIP\n- County of Residence\n- Customer ID\n- VIN\n- Year\n- Make\n- Model\n- Body Style\n- Odometer Reading\n- Lien Holder Name\n- Lien Holder Address\n- Dealer Name\n- Dealer Number\n- Sale Price\n\nRecognize these alternate labels:\n\n- Buyer Full Name: Buyer, Purchaser, Owner Name\n- Co-Buyer Name: Co-Buyer, Co-Purchaser\n- Buyer Address: Address, Residence, Street Address\n- Customer ID: DL#, Driver's License Number, DLN\n- VIN: Vehicle ID Number, VIN#\n- Year: YR\n- Make: Vehicle Make\n- Model: Vehicle Model\n- Body Style: Body Type\n- Odometer Reading: ODO, Mileage\n- Sale Price: Purchase Price, Selling Price\n- ZIP: Zip Code, Postal Code\n- Dealer Number: Dealer ID, License #\n- Lien Holder Name: Lender Name\n- Lien Holder Address: Lender Address, Secured Party Address\n\nUse \"N/A\" if a value is missing or not present.\n\n⚠️ Respond with valid, clean JSON only. No explanations or extra formatting.\n\n--- DOCUMENT START ---\n{{ document_text }}\n--- DOCUMENT END ---\n", "template_name": "title_application.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.745Z"}}, {"_id": {"$oid": "687e81e009904e8924b3713f"}, "document_type": "red_reassignment", "content": "You are a document extraction assistant. Extract and normalize the following MV-7D fields from the text below and return only valid JSON:\n\nRequired Fields:\n\n- VIN  \n- Year  \n- Make  \n- Model  \n- Odometer Reading  \n- Odometer Type  \n- Buyer Name  \n- Buyer Address  \n- Date of Reassignment  \n- Signatures  \n\nRecognize alternate label formats:\n\n- VIN: Vehicle ID, VIN #  \n- Odometer Type: ACTUAL, EXCEEDS, NOT ACTUAL, Mileage Type  \n- Odometer Reading: Miles, Mileage  \n- Buyer Name: Purchaser, New Owner  \n- Buyer Address: Address, Street Address, Residence  \n- Date of Reassignment: Reassignment Date, Title Transfer Date  \n- Signatures: Seller Signature, Buyer Signature, Signatures\n\nNormalize the date format for \"Date of Reassignment\" as MM/DD/YYYY (e.g., 02/02/2022).  \nUse \"N/A\" if any value is missing or not found.\n\n⚠️ Respond with valid, clean JSON only. No explanations or formatting.\n\n--- DOCUMENT START ---\n{{ document_text }}\n--- DOCUMENT END ---\n", "template_name": "red_reassignment.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.745Z"}}, {"_id": {"$oid": "687e81e009904e8924b37141"}, "document_type": "title", "content": "You are a data extraction assistant. Your task is to extract and normalize the following fields from a vehicle title document, regardless of how they are labeled (abbreviated or spelled differently):\n\nExtract these fields and map them to these exact names in your output:\n- VIN\n- Year\n- Make\n- Model\n- Body Style\n- Odometer Reading\n- Selling Dealer Name\n- Lien Holder Name\n- Lien Satisfied\n- Date of Transfer\n- Buyer Name\n- Title Number\n- Seller Signature\n\nRecognize fields even if they are labeled differently:\n\n- VIN: Vehicle ID Number, VIN No., VIN #\n- Year: YR, Model Year\n- Make: Manufacturer, Brand\n- Model: Model Name\n- Body Style: Body Type, Style\n- Odometer Reading: ODO, Miles, Mileage\n- Selling Dealer Name: Selling Dealer, Dealer Name, Dealership\n- Lien Holder Name: Lienholder, Finance Company\n- Lien Satisfied: Lien Released, Lien Paid\n- Date of Transfer: Transfer Date, Date Reassigned\n- Buyer Name: Purchaser, New Owner, Customer\n- Title Number: Title No., Certificate Number, State Title #\n- Seller Signature: Seller Signed, Owner Signature, Signature of Seller\n\n⚠️ Normalize all values to the field names above.  \n⚠️ Format \"Date of Transfer\" as MM/DD/YYYY (e.g. 02/02/2022).  \n⚠️ If a value is missing or not found, use \"N/A\".  \n✅ Respond with only valid, clean JSON (no explanation).\n\n--- DOCUMENT TEXT START ---\n{{ document_text }}\n--- DOCUMENT TEXT END ---\n", "template_name": "title.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.747Z"}}]