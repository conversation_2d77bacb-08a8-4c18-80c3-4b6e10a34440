Resources:
  LlmExtractorRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:custom.servicePrefix}-llm_extractor
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                Service: lambda.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: ${self:custom.servicePrefix}-LlmExtractor
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                # CloudWatch Logs permissions
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                  Resource: "arn:aws:logs:*:*:*"
                - Effect: Allow
                  Action:
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                  Resource:
                    - "arn:aws:logs:*:*:log-group:/aws/lambda/${self:custom.servicePrefix}-llm_extractor:*"
                # Secrets Manager permissions
                - Effect: Allow
                  Action:
                    - secretsmanager:GetSecretValue
                    - secretsmanager:ListSecrets
                  Resource: "*"
                # Systems Manager permissions
                - Effect: Allow
                  Action:
                    - ssm:GetParameter
                  Resource: "*"
                # EC2 permissions (for VPC connectivity)
                - Effect: Allow
                  Action:
                    - ec2:DescribeNetworkInterfaces
                    - ec2:CreateNetworkInterface
                    - ec2:DeleteNetworkInterface
                  Resource: "*"
                # KMS permissions (for encrypted resources)
                - Effect: Allow
                  Action:
                    - kms:DescribeKey
                    - kms:ListAliases
                    - kms:ListKeys
                  Resource: "*"
                # Lambda permissions (if this Lambda interacts with other Lambdas)
                - Effect: Allow
                  Action:
                    - lambda:AddPermission
                    - lambda:CreateFunction
                    - lambda:GetFunction
                    - lambda:InvokeFunction
                    - lambda:UpdateFunctionConfiguration
                  Resource:
                    - "arn:aws:lambda:*:*:function:*"
                    - "*"
                # CloudFormation permissions
                - Effect: Allow
                  Action:
                    - cloudformation:DescribeStacks
                    - cloudformation:CreateChangeSet
                    - cloudformation:DescribeChangeSet
                    - cloudformation:ExecuteChangeSet
                  Resource: "*"
                # Additional AWS services or actions
                - Effect: Allow
                  Action:
                    - rds:DescribeDBClusters
                    - rds:DescribeDBInstances
                    - redshift:DescribeClusters
                  Resource: "*"
                - Effect: "Allow"
                  Action:
                    - logs:*
                    - cloudwatch:GenerateQuery
                  Resource:
                    - "*"