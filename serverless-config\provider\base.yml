provider:
  name: aws
  stage: ${opt:stage, 'local'}
  region: ${env:REGION}
  versionFunctions: false
  deploymentBucket:
    name: ${env:DEPLOYMENT_BUCKET_NAME}
  stackTags:
    STAGE: ${self:provider.stage, 'local'}
    Subproject: "App"
    Project: "Serverless"
  tracing:
    apiGateway: true
  vpc: ${file(./serverless-config/provider/vpc.yml):vpc}
  environment: ${file(./serverless-config/provider/environment.yml):environment}

  httpApi:
    authorizers:
      lambda_authorizer:
        type: request
        functionName: lambda_authorizer
        identitySource: $request.header.Authorization
        resultTtlInSeconds: 300
        enableSimpleResponses: true
        payloadVersion: "2.0"
