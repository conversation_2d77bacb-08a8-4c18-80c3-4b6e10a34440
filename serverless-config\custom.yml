custom:
  hooks:
    hook:before:package:initialize: bash scripts/prepackage.sh
  stage: ${opt:stage, 'local'}
  servicePrefix: ${self:provider.stage}-${env:PROJECT_NAME}-${env:PROCESS_NAME}
  localstack:
    stages:
      - local
    host: http://localhost
  dotenv:
    include:
      - REGION
      - DATABASE_NAME
      - ENV
      - PROJECT_NAME
      - COMMON_PREFIX
      - ARIA_DATABASE
      - PROCESS_NAME
      - ARIA_ENVIRONMENT
      - LLM_EXTRACTOR_COLLECTION_NAME
